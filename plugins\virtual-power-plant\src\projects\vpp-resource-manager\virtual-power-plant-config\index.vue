<template>
  <div class="vpp-info-page">
    <div class="vpp-info-container">
      <!-- 页面标题和操作按钮 -->
      <div class="page-header">
        <h2 class="page-title">{{ $T("虚拟电厂厂站信息") }}</h2>
        <el-button type="primary" @click="handleConfigVpp">
          {{ $T("配置虚拟电厂") }}
        </el-button>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 虚拟电厂基本信息卡片 -->
        <div class="vpp-info-section">
          <!-- 左侧：图片回显 -->
          <div class="vpp-image-container">
            <FileImage
              v-if="vppInfo.picturePath"
              fit="contain"
              style="height: 200px; width: 300px"
              :source="vppInfo.picturePath"
              :placeholderImg="defaultImage"
              :onHandleImgSrc="
                fileName =>
                  `/vpp/api/v1/resource-manager/base-config/images/download/${fileName}`
              "
              hideNotice
            />
            <div v-else class="no-image-placeholder">
              <img
                :src="defaultImage"
                alt="默认虚拟电厂图片"
                style="height: 200px; width: 300px; object-fit: contain"
              />
            </div>
          </div>

          <!-- 右侧：基本信息 -->
          <div class="vpp-basic-info">
            <!-- 第一行 -->
            <el-row :gutter="24">
              <el-col :span="8">
                {{ $T("虚拟电厂名称：") }}{{ vppInfo.vppName || "--" }}
              </el-col>
              <el-col :span="8">
                {{ $T("虚拟电厂所属省份：")
                }}{{ getProvinceNameByCode(vppInfo.province) || "--" }}
              </el-col>
              <el-col :span="8">
                {{ $T("虚拟电厂站类型：")
                }}{{ getVppTypeText(vppInfo.vppType) || "--" }}
              </el-col>
            </el-row>
            <!-- 第二行 -->
            <el-row :gutter="24">
              <el-col :span="8">
                {{ $T("虚拟电厂成立时间：") }}{{ vppInfo.createTime || "--" }}
              </el-col>
              <el-col :span="8">
                {{ $T("运营商编号：") }}{{ vppInfo.operatorcode || "--" }}
              </el-col>
            </el-row>
            <!-- 第三行 -->
            <el-row :gutter="24">
              <el-col :span="16">
                {{ $T("虚拟电厂描述：") }}{{ vppInfo.description || "--" }}
              </el-col>
            </el-row>

            <!-- 技术参数卡片区域 -->
            <div>
              <el-row :gutter="16">
                <!-- 需求响应卡片 -->
                <el-col :span="8">
                  <el-card class="param-card" body-style="padding: 16px;">
                    <h4 class="card-title">{{ $T("需求响应") }}</h4>
                    <div class="card-content">
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格上限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ vppInfo.demandResponseDeclaredPriceCaps || "--" }}
                        </span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格下限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ vppInfo.demandResponseFilingPriceCaps || "--" }}
                        </span>
                      </div>
                    </div>
                  </el-card>
                </el-col>

                <!-- 调峰卡片 -->
                <el-col :span="8">
                  <el-card class="param-card" body-style="padding: 16px;">
                    <h4 class="card-title">{{ $T("调峰") }}</h4>
                    <div class="card-content">
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格上限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ vppInfo.peakingDeclaredPriceFloor || "--" }}
                        </span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格下限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ vppInfo.peakingFilingPriceFloor || "--" }}
                        </span>
                      </div>
                    </div>
                  </el-card>
                </el-col>

                <!-- 调频卡片 -->
                <el-col :span="8">
                  <el-card class="param-card" body-style="padding: 16px;">
                    <h4 class="card-title">{{ $T("调频") }}</h4>
                    <div class="card-content">
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格上限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ vppInfo.pmDeclaredPriceFloor || "--" }}
                        </span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格下限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ vppInfo.pmFilingPriceFloor || "--" }}
                        </span>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>

              <!-- 第二行：调节和能量卡片 -->
              <el-row :gutter="16" style="margin-top: 16px">
                <!-- 调节卡片 - 仅当类型包含调节型时显示 -->
                <el-col v-if="isRegulationType" :span="8">
                  <el-card class="param-card" body-style="padding: 16px;">
                    <h4 class="card-title">{{ $T("调节") }}</h4>
                    <div class="card-content">
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格上限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ vppInfo.adjustDeclaredPriceFloor || "--" }}
                        </span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格下限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ vppInfo.adjustFilingPriceFloor || "--" }}
                        </span>
                      </div>
                    </div>
                  </el-card>
                </el-col>

                <!-- 能量卡片 - 仅当类型包含能量型时显示 -->
                <el-col v-if="isEnergyType" :span="8">
                  <el-card class="param-card" body-style="padding: 16px;">
                    <h4 class="card-title">{{ $T("能量") }}</h4>
                    <div class="card-content">
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格上限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ vppInfo.energyResponseDeclaredPriceFloor || "--" }}
                        </span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">
                          {{ $T("申报价格下限（元/MWh）") }}
                        </span>
                        <span class="param-value">
                          {{ vppInfo.energyResponseFilingPriceFloor || "--" }}
                        </span>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>

        <!-- 虚拟电厂资源统计 -->
        <div class="stats-section">
          <div class="stats-header">
            <h3 class="stats-title">{{ $T("虚拟电厂资源统计") }}</h3>
          </div>
          <div class="stats-cards">
            <div class="stats-card">
              <div class="stats-icon">
                <img
                  src="static/image/enterprise_user_count.png"
                  alt="用户图标"
                  class="icon-image"
                />
              </div>
              <div class="stats-info">
                <div class="stats-label">
                  {{ $T("接入企业/用户数量（家）") }}
                </div>
                <div class="stats-value">
                  {{ vppInfo.userCount || "--" }}
                </div>
              </div>
            </div>

            <div class="stats-card">
              <div class="stats-icon">
                <img
                  src="static/image/resource_count.png"
                  alt="资源图标"
                  class="icon-image"
                />
              </div>
              <div class="stats-info">
                <div class="stats-label">{{ $T("聚合资源数量（个）") }}</div>
                <div class="stats-value">
                  {{ vppInfo.resourceCount || "--" }}
                </div>
              </div>
            </div>

            <div class="stats-card">
              <div class="stats-icon">
                <img
                  src="static/image/capacity.png"
                  alt="容量图标"
                  class="icon-image"
                />
              </div>
              <div class="stats-info">
                <div class="stats-label">{{ $T("可调节容量（MW）") }}</div>
                <div class="stats-value">
                  {{ vppInfo.adjustableCapacity || "--" }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ConfigDialog
        :visible.sync="configDialogVisible"
        :vpp-data="vppInfo"
        :province-options="provinceOptions"
        @confirm="handleConfigConfirm"
      />
    </div>
  </div>
</template>

<script>
import ConfigDialog from "./components/ConfigDialog.vue";
// import FileImage from "./components/FileImage.vue";
import { getVppById, updateVpp } from "@/api/vpp-config";
import { getGeographicalData } from "@/api/base-config";
import { getEnumOptions } from "@/utils/enumManager";

export default {
  components: {
    ConfigDialog
    // FileImage
  },
  name: "VirtualPowerPlantInfo",
  data() {
    return {
      // 默认电厂图片 - 使用占位符
      defaultImage:
        "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik04MCA2MEg0MFY5MEg4MFY2MFoiIGZpbGw9IiNEREREREQiLz4KPHA+dGggZD0iTTE2MCA2MEgxMjBWOTBIMTYwVjYwWiIgZmlsbD0iI0RERERERCIvPgo8cGF0aCBkPSJNMTAwIDMwSDEwMFY2MEgxMDBWMzBaIiBzdHJva2U9IiNEREREREQiIHN0cm9rZS13aWR0aD0iMiIvPgo8dGV4dCB4PSIxMDAiIHk9IjEyMCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzk5OTk5OSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIj7ooZXmi6XnlLXljoI8L3RleHQ+Cjwvc3ZnPgo=",
      // 虚拟电厂信息 - 与接口返回数据结构完全一致
      vppInfo: {
        id: null,
        vppName: "",
        province: null,
        vppType: null,
        description: null,
        userCount: null,
        resourceCount: null,
        operatorcode: null,
        demandResponseFilingPriceCaps: null,
        demandResponseDeclaredPriceCaps: null,
        peakingDeclaredPriceFloor: null,
        peakingFilingPriceFloor: null,
        pmDeclaredPriceFloor: null,
        pmFilingPriceFloor: null,
        adjustDeclaredPriceFloor: null,
        adjustFilingPriceFloor: null,
        energyResponseDeclaredPriceFloor: null,
        energyResponseFilingPriceFloor: null,
        picturePath: null,
        createTime: null,
        updateTime: null,
        vpp_user_list: [],
        adjustableCapacity: null
      },
      configDialogVisible: false,
      provinceOptions: []
    };
  },
  computed: {
    // 是否包含调节型
    isRegulationType() {
      return (
        this.vppInfo.vppType && this.vppInfo.vppType.toString().includes("3")
      );
    },
    // 是否包含能量型
    isEnergyType() {
      return (
        this.vppInfo.vppType && this.vppInfo.vppType.toString().includes("4")
      );
    }
  },

  methods: {
    // 加载虚拟电厂数据
    async loadVppData() {
      try {
        // 调用API获取ID为1的虚拟电厂数据
        const response = await getVppById(1);
        if (response.code === 0 && response.data) {
          // 直接使用接口返回的数据，保持数据结构完全一致
          this.vppInfo = {
            ...response.data,
            createTime: this.formatCreateTime(response.data.createTime)
          };
        } else {
          this.$message.error(response.msg || "获取虚拟电厂数据失败");
        }
      } catch (error) {
        console.error("加载虚拟电厂数据失败:", error);
        this.$message.error("加载数据失败");
      }
    },

    // 配置虚拟电厂
    handleConfigVpp() {
      this.configDialogVisible = true;
    },

    async handleConfigConfirm(data) {
      try {
        // 调用更新接口更新数据
        const param = {
          name: data.vppName,
          ...data
        };
        const response = await updateVpp(data.id, param);
        if (response.code === 0) {
          // 更新成功后，重新调用接口获取最新数据
          await this.loadVppData();
          this.$message.success(this.$T("虚拟电厂配置更新成功"));
          this.configDialogVisible = false;
        } else {
          this.$message.error(response.msg || this.$T("更新失败"));
        }
      } catch (error) {
        console.error("更新虚拟电厂配置失败:", error);
        this.$message.error(this.$T("更新失败，请稍后重试"));
      }
    },
    // 加载地理数据
    async loadGeographicalData() {
      try {
        const response = await getGeographicalData();
        if (response.code === 0 && response.data) {
          // 提取省份数据
          this.provinceOptions = response.data.map(province => ({
            code: province.code,
            name: province.name
          }));
        } else {
          console.error("加载地理数据失败:", response.msg);
          this.$message.error(this.$T("加载省份数据失败"));
        }
      } catch (error) {
        console.error("加载地理数据失败:", error);
        this.$message.error(this.$T("加载省份数据失败"));
      }
    },

    // 根据省份代码获取省份名称
    getProvinceNameByCode(code) {
      const province = this.provinceOptions.find(p => p.code === code);
      return province ? province.name : code;
    },

    // 根据VPP类型获取类型文本（支持逗号分隔的多选）
    getVppTypeText(type) {
      if (!type) return "";

      // 使用枚举获取VPP类型映射
      const enumOptions = getEnumOptions("VPP_TYPE");
      const typeMap = {};
      enumOptions.forEach(option => {
        typeMap[option.value] = option.label;
      });

      // 如果是逗号分隔的字符串，解析并组合
      const types = type
        .toString()
        .split(",")
        .map(t => t.trim());
      const typeTexts = types.map(t => typeMap[t]).filter(Boolean);

      return typeTexts.join(" / ") || "";
    },

    // 格式化创建时间
    formatCreateTime(timestamp) {
      if (!timestamp) return null;

      // 将时间戳转换为Date对象
      const date = new Date(timestamp);

      // 检查日期是否有效
      if (isNaN(date.getTime())) return null;

      // 转换为ISO格式字符串 (YYYY-MM-DDTHH:mm:ss.sssZ)
      return date.toISOString();
    }
  },
  mounted() {
    this.loadVppData();
    this.loadGeographicalData();
  }
};
</script>

<style scoped lang="scss">
.vpp-info-page {
  height: 100%;
  background: var(--BG1);
}
.vpp-info-container {
  padding: var(--J4);
}
/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--J3);
}

.page-title {
  font-size: var(--H3);
  color: var(--T1);
  margin: 0;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  flex-direction: column;
  gap: var(--J4);
}

/* 虚拟电厂基本信息 */
.vpp-info-section {
  display: flex;
  gap: var(--J3);
}

.vpp-image-container {
  width: 300px;
  height: 200px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--BG1);
}

.no-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 基本信息模块 */
.vpp-basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--J4);
}

/* 技术参数卡片 */
.param-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-radius: var(--Ra);
  border: none;
  background-color: var(--BG4);
}

.card-title {
  font-size: var(--Aa);
  color: var(--T2);
  margin: 0 0 var(--J2) 0;
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--J4);
  flex: 1;
}

.param-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--J1);
}

.param-label {
  font-size: var(--Aa);
  color: var(--T3);
}

.param-value {
  color: var(--T1);
}

/* 资源统计区域 */
.stats-section {
  display: flex;
  flex-direction: column;
  gap: var(--J3);
}

.stats-header {
  display: flex;
  align-items: center;
}

.stats-title {
  font-size: var(--H3);
  color: var(--T1);
  margin: 0;
}

.stats-cards {
  display: flex;
  gap: var(--J3);
}

.stats-card {
  flex: 1;
  background: var(--BG4);
  border-radius: var(--Ra);
  padding: var(--J3);
  height: 88px;
  display: flex;
  align-items: center;
  gap: var(--J3);
}

.stats-icon {
  width: 56px;
  height: 56px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--Ra);
}

.icon-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.stats-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stats-label {
  font-size: var(--Aa);
  color: var(--T3);
  margin-bottom: var(--J0);
}

.stats-value {
  font-size: var(--H);
  color: var(--T1);
}
</style>
